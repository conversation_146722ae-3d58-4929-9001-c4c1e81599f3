<?php

namespace App\Repositories;

use App\Models\Article;
use App\Models\Category;
use App\Repositories\Interfaces\ArticleRepositoryInterface;
use Illuminate\Support\Facades\DB;

class ArticleRepository implements ArticleRepositoryInterface
{
    public function getAll($paginate = true, $limit = null)
    {
        $query = Article::
            where('type', 'article')
            ->where('status', 'published')
            ->where('active', true)
            ->latest();

        if ($paginate) {
            return $query->paginate($limit ?? 9);
        }

        if ($limit) {
            return $query->limit($limit)->get();
        }

        return $query->get();
    }

    public function getByCategorySlug($slug, $paginate = true, $limit = null)
    {
        $query = Article::whereHas('category', fn($q) => $q->where('slug', $slug))
            ->where('type', 'article')
            ->where('active', true)
            ->where('status', 'published')
            ->latest();

        if ($paginate) {
            return $query->paginate($limit ?? 9);
        }

        return $limit
            ? $query->limit($limit)->get()
            : $query->get();
    }

    public function getEditorPicks($paginate = true, $limit = null)
    {
        $query = Article::where('type', 'article')
            ->where('status', 'published')
            ->where('active', true)
            ->where('editor_recommendation', true)
            ->latest();

        if ($paginate) {
            return $query->paginate($limit ?? 9);
        }

        if ($limit) {
            return $query->limit($limit)->get();
        }

        return $query->get();
    }

    public function getEditorPicksByCategorySlug($slug, $paginate = true, $limit = null)
    {
        $query = Article::whereHas('category', fn($q) => $q->where('slug', $slug))
            ->where('type', 'article')
            ->where('active', true)
            ->where('status', 'published')
            ->where('editor_recommendation', true)
            ->latest();

        if ($paginate) {
            return $query->paginate($limit ?? 9);
        }

        return $limit
            ? $query->limit($limit)->get()
            : $query->get();
    }

    public function findByPageSlug($page)
    {
        $article = Article::where('page', '/' . $page)
            ->where('status', 'published')
            ->where('active', true)
            ->first();


        if ($article) {
            $this->incrementViewCount($article->id);
        }

        return $article;
    }

    public function findByArticleSlug($slug)
    {
        $article = Article::where('slug', $slug)
            ->where('active', true)
            ->where('status', 'published')
            ->latest()
            ->first();


        if ($article) {
            $this->incrementViewCount($article->id);
        }

        return $article;
    }

    public function incrementViewCount($id, $connection = 'mongodb')
    {
        $article = DB::connection($connection)->table('articles')->where('id', $id)->first();

        if ($article) {

            $currentViewCount = $article->view_count ?? 0;

            DB::connection($connection)->table('articles')
                ->where('id', $id)
                ->update(['view_count' => $currentViewCount + 1]);
        }
    }
}
