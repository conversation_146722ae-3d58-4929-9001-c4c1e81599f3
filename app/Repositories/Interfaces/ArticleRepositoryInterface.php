<?php

namespace App\Repositories\Interfaces;

interface ArticleRepositoryInterface
{
    public function getAll($paginate = true, $limit = null, $editorChoice = null);

    public function getByCategorySlug($slug, $paginate = true, $limit = null, $editorChoice = null);

    public function getEditorPicks($paginate = true, $limit = null);

    public function getEditorPicksByCategorySlug($slug, $paginate = true, $limit = null);

    public function findByPageSlug($slug);

    public function findByArticleSlug($slug);

    public function incrementViewCount($id, $connection = 'mongodb');
}
