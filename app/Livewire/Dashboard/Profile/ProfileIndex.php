<?php

namespace App\Livewire\Dashboard\Profile;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\WithFileUploads;

class ProfileIndex extends Component
{
    use LivewireAlert, WithFileUploads;

    #[Validate('required|string|max:255')]
    public $fullname;

    #[Validate('nullable|string|max:1000')]
    public $about;

    #[Validate('nullable|image|max:2048')]
    public $avatar;

    public $current_avatar;
    public $phone;
    public $roles;
    public $permissions;
    public $source;
    public $uploading = false;
    public $removing = false;
    public $saving = false;

    public function mount()
    {
        $user = Auth::user();

        $this->fullname = $user->fullname;
        $this->about = $user->about;
        $this->current_avatar = $user->avatar;
        $this->phone = $user->phone;
        $this->source = $user->source;

        // Get user roles and permissions
        $this->roles = $user->roles->pluck('label')->implode(', ') ?: 'کاربر عادی';
        $this->permissions = $user->roles
            ->flatMap(function ($role) {
                return $role->permissions->pluck('name');
            })
            ->unique()
            ->values()
            ->toArray();
    }

    public function updatedAvatar()
    {
        $this->validate([
            'avatar' => 'image|max:2048', // 2MB Max
        ]);

        $this->uploading = true;

        // Upload and save avatar immediately
        $user = Auth::user();
        $path = saveFileFromTemporaryImage($this->avatar, 'avatars');

        if ($path) {
            $user->update(['avatar' => $path]);
            $this->current_avatar = asset($path);

            $this->alert('success', 'تصویر پروفایل با موفقیت بروزرسانی شد', [
                'position' => 'top-start',
            ]);
        }

        $this->uploading = false;
        $this->avatar = null;
    }

    public function save()
    {
        $this->saving = true;

        $this->validate();

        $user = Auth::user();

        $updateData = [
            'fullname' => $this->fullname,
            'about' => $this->about,
        ];

        $user->update($updateData);

        $this->alert('success', 'پروفایل با موفقیت بروزرسانی شد', [
            'position' => 'top-start',
        ]);

        $this->saving = false;
    }

    public function removeAvatar()
    {
        $this->removing = true;

        $user = Auth::user();
        $user->update(['avatar' => null]);

        $this->current_avatar = null;

        $this->alert('success', 'تصویر پروفایل حذف شد', [
            'position' => 'top-start',
        ]);

        $this->removing = false;
    }

    public function render()
    {
        return view('livewire.dashboard.profile.profile-index');
    }
}
