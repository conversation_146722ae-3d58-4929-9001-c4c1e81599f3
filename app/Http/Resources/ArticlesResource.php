<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticlesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'title' => $this->title ?? null,
            'slug' => $this->slug ?? null,
            'summary' => $this->summary ?? null,
            'view_count' => $this->view_count ?? 0,
            'comments_count' => 3,
            // 'author' => $this->author?->fullname,
            'author_fullname' => $this->author->fullname ?? 'مدیر سایت',
            'author_avatar' => $this->author->avatar ?? null,
            'author_about' => $this->author->about ?? 'خودراکس یک پلتفرم جامع برای دریافت خدمات آنلاین مربوط به خلافی خودرو، موتورسیکلت و سایر سرویس‌های مرتبط با وسایل نقلیه است. همچنین، این سایت دارای فروشگاه اختصاصی برای محصولات مرتبط می‌باشد.',
            'study_time' => $this->study_time . ' دقیقه ',
            'cover' => 'https://dl.khodrox.com/' . $this->galleries()?->latest()?->first()?->image ?? null,
            'date' => shamsiDate($this->updated_at) ?? null,
            'category' => [
                'title' => $this->category->title ?? null,
                'slug' => $this->category->slug ?? null,
            ],
        ];
    }
}
