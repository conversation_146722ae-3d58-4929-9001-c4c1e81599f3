<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title ?? null,
            'meta_title' => $this->meta_title ?? null,
            'meta_description' => $this->meta_description ?? null,
            'description' => $this->description ?? null,
            'schema' => $this->schema ?? null,
            'tags' => $this->tags ?? null,
            'faqs' => $this->faqs ?? null,
            'meta_search' => $this?->meta_search ?? null,
            'canonical' => $this?->canonical ?? null,
            'view_count' => $this->view_count ?? 0,
            'comments' => [],
            'comments_count' => 0,
            'category' => [
                'title' => $this->category->title ?? null,
                'slug' => $this->category->slug ?? null,
            ],
            'date_ago' => get_ago($this->created_at),
            'author_fullname' => $this->author->fullname ?? 'مدیر سایت',
        ];
    }
}
