<?php

namespace Tests\Feature;

use App\Models\Article;
use App\Repositories\ArticleRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ArticleRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected $articleRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->articleRepository = new ArticleRepository();
    }

    /** @test */
    public function it_can_get_all_articles()
    {
        // ایجاد مقالات تست
        Article::factory()->create([
            'type' => 'article',
            'status' => 'published',
            'active' => true,
            'editor_recommendation' => false,
        ]);

        Article::factory()->create([
            'type' => 'article',
            'status' => 'published',
            'active' => true,
            'editor_recommendation' => true,
        ]);

        // تست دریافت همه مقالات
        $allArticles = $this->articleRepository->getAll(false);
        $this->assertCount(2, $allArticles);

        // تست دریافت فقط مقالات منتخب سردبیر
        $editorChoiceArticles = $this->articleRepository->getAll(false, null, true);
        $this->assertCount(1, $editorChoiceArticles);
        $this->assertTrue($editorChoiceArticles->first()->editor_recommendation);

        // تست دریافت فقط مقالات غیر منتخب سردبیر
        $nonEditorChoiceArticles = $this->articleRepository->getAll(false, null, false);
        $this->assertCount(1, $nonEditorChoiceArticles);
        $this->assertFalse($nonEditorChoiceArticles->first()->editor_recommendation);
    }

    /** @test */
    public function it_can_get_articles_by_category_with_editor_choice_filter()
    {
        // ایجاد دسته‌بندی
        $category = \App\Models\Category::factory()->create(['slug' => 'test-category']);

        // ایجاد مقالات تست
        Article::factory()->create([
            'category_id' => $category->id,
            'type' => 'article',
            'status' => 'published',
            'active' => true,
            'editor_recommendation' => false,
        ]);

        Article::factory()->create([
            'category_id' => $category->id,
            'type' => 'article',
            'status' => 'published',
            'active' => true,
            'editor_recommendation' => true,
        ]);

        // تست دریافت همه مقالات دسته‌بندی
        $allCategoryArticles = $this->articleRepository->getByCategorySlug('test-category', false);
        $this->assertCount(2, $allCategoryArticles);

        // تست دریافت فقط مقالات منتخب سردبیر از دسته‌بندی
        $editorChoiceCategoryArticles = $this->articleRepository->getByCategorySlug('test-category', false, null, true);
        $this->assertCount(1, $editorChoiceCategoryArticles);
        $this->assertTrue($editorChoiceCategoryArticles->first()->editor_recommendation);
    }
}
