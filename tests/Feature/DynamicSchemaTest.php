<?php

namespace Tests\Feature;

use App\Models\Article;
use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DynamicSchemaTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_render_article_create_page_with_dynamic_schema()
    {
        // ایجاد کاربر تست
        $user = User::factory()->create();
        $this->actingAs($user);

        // ایجاد دسته‌بندی تست
        Category::factory()->create(['title' => 'تست', 'slug' => 'test']);

        // درخواست صفحه ایجاد مقاله
        $response = $this->get('/dashboard/articles/create');

        $response->assertStatus(200);
        $response->assertSee('generateDynamicSchema');
        $response->assertSee('🔧 صفحات فرزند (دینامیک)');
        $response->assertSee('📝 بلاگ + FAQ (دینامیک)');
    }

    /** @test */
    public function it_can_render_article_edit_page_with_dynamic_schema()
    {
        // ایجاد کاربر تست
        $user = User::factory()->create();
        $this->actingAs($user);

        // ایجاد دسته‌بندی و مقاله تست
        $category = Category::factory()->create(['title' => 'تست', 'slug' => 'test']);
        $article = Article::factory()->create([
            'title' => 'مقاله تست',
            'slug' => 'test-article',
            'category_id' => $category->id,
            'meta_title' => 'متا تایتل تست',
            'meta_description' => 'متا توضیحات تست',
            'faqs' => [
                ['question' => 'سوال تست', 'answer' => 'پاسخ تست']
            ]
        ]);

        // درخواست صفحه ویرایش مقاله
        $response = $this->get("/dashboard/articles/{$article->id}");

        $response->assertStatus(200);
        $response->assertSee('generateDynamicSchema');
        $response->assertSee('🔧 صفحات فرزند (دینامیک)');
        $response->assertSee('📝 بلاگ + FAQ (دینامیک)');
    }

    /** @test */
    public function dynamic_schema_javascript_functions_are_present()
    {
        // ایجاد کاربر تست
        $user = User::factory()->create();
        $this->actingAs($user);

        // ایجاد دسته‌بندی تست
        Category::factory()->create(['title' => 'تست', 'slug' => 'test']);

        // درخواست صفحه ایجاد مقاله
        $response = $this->get('/dashboard/articles/create');

        $response->assertStatus(200);
        
        // بررسی وجود توابع JavaScript
        $response->assertSee('generateChildPageSchema');
        $response->assertSee('generateListingPageSchema');
        $response->assertSee('generateHomePageSchema');
        $response->assertSee('generateBlogTemplateSchema');
        $response->assertSee('generateBlogExampleSchema');
        
        // بررسی وجود متغیرهای مورد نیاز
        $response->assertSee('@this.get(\'data\')');
        $response->assertSee('@this.get(\'faqs\')');
        $response->assertSee('@this.set(\'data.schema\'');
    }
}
