<?php

namespace Tests\Feature;

use App\Models\Article;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ArticleApiTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_get_articles_with_editor_choice_filter()
    {
        // ایجاد مقالات تست
        Article::factory()->create([
            'type' => 'article',
            'status' => 'published',
            'active' => true,
            'editor_recommendation' => false,
        ]);

        Article::factory()->create([
            'type' => 'article',
            'status' => 'published',
            'active' => true,
            'editor_recommendation' => true,
        ]);

        // تست دریافت همه مقالات
        $response = $this->getJson('/api/v1/articles');
        $response->assertStatus(200);
        $this->assertCount(2, $response->json('data'));

        // تست دریافت فقط مقالات منتخب سردبیر
        $response = $this->getJson('/api/v1/articles?editor_choice=1');
        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));

        // تست دریافت فقط مقالات غیر منتخب سردبیر
        $response = $this->getJson('/api/v1/articles?editor_choice=0');
        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
    }

    /** @test */
    public function it_can_get_articles_by_category_with_editor_choice_filter()
    {
        // ایجاد دسته‌بندی
        $category = Category::factory()->create(['slug' => 'test-category']);

        // ایجاد مقالات تست
        Article::factory()->create([
            'category_id' => $category->id,
            'type' => 'article',
            'status' => 'published',
            'active' => true,
            'editor_recommendation' => false,
        ]);

        Article::factory()->create([
            'category_id' => $category->id,
            'type' => 'article',
            'status' => 'published',
            'active' => true,
            'editor_recommendation' => true,
        ]);

        // تست دریافت همه مقالات دسته‌بندی
        $response = $this->getJson('/api/v1/articles?category=test-category');
        $response->assertStatus(200);
        $this->assertCount(2, $response->json('data'));

        // تست دریافت فقط مقالات منتخب سردبیر از دسته‌بندی
        $response = $this->getJson('/api/v1/articles?category=test-category&editor_choice=1');
        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));

        // تست دریافت فقط مقالات غیر منتخب سردبیر از دسته‌بندی
        $response = $this->getJson('/api/v1/articles?category=test-category&editor_choice=0');
        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
    }
}
