<?php

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Route;



Route::get('/close-request-pending', 'HostCheckerController@closePendingRequests')->name('close-request-pending');

Route::get('/replay-host-checker', 'Host<PERSON>he<PERSON>Controller@replayCheckHost')->name('host-replay-check');

Route::get('/start-host-checker', 'HostCheckerController@checkHost')->name('host-run-check');

Route::get('/get-nodes', 'HostCheckerController@getHttpNodes');

Route::middleware('auth:web')->group(function () {

    Route::view('/', 'dashboard.dashboard-index')->name('dashboard');

    Route::group(['prefix' => 'dashboard'], function () {

        Route::get('test', 'TestController@index');

        Route::get('/report-check', 'Test<PERSON>ontroller@getUptimeChartData');

        Route::view('/host-check', 'dashboard.host-check.host-check-index')->name('host-check-index')->can('show-host-check-index');
        Route::view('/utm-reports', 'dashboard.reports.utm-reports')->name('utm-report')->can('show-utm-report');

        Route::view('/users', 'dashboard.users.users-index')->name('users')->can('show-list-users');
        Route::view('/role-permission', 'dashboard.users.role-permission-index')->name('role-permission')->can('show-list-users');

        Route::view('/profile', 'dashboard.profile.profile-index')->name('profile');

        Route::view('/inquiries-negative-score', 'dashboard.inquiries.inquiries-negative-score')->name('inquiries-negative-score')->can('show-history-inquries-car-motor');
        Route::view('/inquiries-driving-license-status', 'dashboard.inquiries.inquiries-driving-license-status')->name('inquiries-driving-license-status')->can('show-history-inquries-car-motor');
        Route::view('/inquiries-carid-documents', 'dashboard.inquiries.inquiries-carid-documents')->name('inquiries-carid-documents')->can('show-history-inquries-car-motor');
        Route::view('/inquiries-cart-motor', 'dashboard.inquiries.inquiry-index')->name('inquiries-cart-motor')->can('show-history-inquries-car-motor');
        Route::view('/inquiries-shebacart-cart-motor', 'dashboard.inquiries.inquiry-shebacart-index')->name('inquiries-shebacart-cart-motor')->can('show-history-inquries-car-motor');
        Route::view('/inquiries-shebacart-financial', 'dashboard.inquiries.inquiry-shebacart-financial-index')->name('inquiries-shebacart-financial')->can('show-history-inquries-car-motor');
        Route::view('/inquiries-insurance-third-party', 'dashboard.inquiries.inquiry-insurance-third-party-index')->name('inquiries-insurance-third-party')->can('show-history-inquries-car-motor');

        Route::group(['prefix' => 'transactions'], function () {

            Route::view('/', 'dashboard.transactions.transactions-index')->name('transactions')->can('show-report-transaction-site');
            Route::view('/zarinpal', 'dashboard.transactions.transactions-zarinpal')->name('zarinpal-transactions')->can('show-report-transaction-zarinpal-paid');
            Route::view('/zarinpal/refund', 'dashboard.transactions.transactions-refund-zarinpal')->name('zarinpal-refund-transactions')->can('show-report-transaction-zarinpal-refund');
            Route::view('/qabzino', 'dashboard.transactions.transaction-user-qabzino-index')->name('transaction-user-qabzino')->can('show-report-transaction-refund');

        });

        Route::view('/sms', 'dashboard.sms.sms-index')->name('sms')->can('show-payamak');
        Route::view('/notification-manager', 'dashboard.notification.notification-manager-index')->name('notification-manager')->can('show-payamak');

        Route::view('/refund', 'dashboard.refund.refund-index')->name('refound')->can('show-report-transaction-refund');

        Route::view('/categories', 'dashboard.categories.categories-index')->name('categories');

        Route::group(['prefix' => 'articles'], function () {
            Route::view('/', 'dashboard.articles.articles-index')->name('article-manager')->can('show-articles');
            Route::view('/create', 'dashboard.articles.article-create')->name('article-create')->can('show-create-article');
            Route::view('/{articleId}/show', 'dashboard.articles.article-show')->name('article-show')->can('show-show-article');
            Route::view('/comments', 'dashboard.comments.articles-comments-index')->name('comments-articles')->can('show-articles');
        });

        // Sitemap management routes
        Route::group(['prefix' => 'sitemap'], function () {
            Route::view('/', 'dashboard.sitemap.index')->name('sitemap.index');
            Route::post('/generate', 'Dashboard\SitemapController@generate')->name('sitemap.generate');
            Route::post('/upload-ftp', 'Dashboard\SitemapController@uploadToFtp')->name('sitemap.upload-ftp');
            Route::post('/test-ftp', 'Dashboard\SitemapController@testFtp')->name('sitemap.test-ftp');
            Route::get('/download/{filename}', 'Dashboard\SitemapController@download')->name('sitemap.download');
            Route::get('/view/{filename}', 'Dashboard\SitemapController@view')->name('sitemap.view');
        });

        Route::post('upload/image', 'ArticleController@store')->name('ckeditor.upload');

    });

});

require __DIR__ . '/auth.php';

Route::get('/cache-clear', function () {
    Artisan::call('optimize:clear');
    dd('cache clear All');
});
