<?php

use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Api\\v1', 'prefix' => 'v1'], function () {

    Route::get('/categories', 'CategoryController@index');

    Route::get('/articles', 'ArticleController@index');

    // Route::get('/articles/editor-picks', 'ArticleController@editorPicks')->name('articles.editor-picks');

    Route::get('/article/view/{slug}', 'ArticleController@slug');

    Route::get('/article/{page}', 'ArticleController@show')->where('page', '.*');


});
