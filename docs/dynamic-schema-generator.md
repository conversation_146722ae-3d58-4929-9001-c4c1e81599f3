# سیستم تولید اسکیمای دینامیک

## معرفی

سیستم تولید اسکیمای دینامیک به شما امکان ایجاد اسکیماهای JSON-LD بر اساس اطلاعات واقعی مقاله را می‌دهد. به جای استفاده از قالب‌های استاتیک، این سیستم از داده‌های مقاله شما (عنوان، توضیحات، سوالات متداول و...) برای تولید اسکیمای کاستوم استفاده می‌کند.

## ویژگی‌ها

### 🔧 اسکیماهای دینامیک جدید:
- **صفحات فرزند (دینامیک)**: اسکیمای Service با اطلاعات واقعی مقاله
- **صفحات لیستینگ (دینامیک)**: اسکیمای Service + ItemList با اطلاعات واقعی
- **صفحه Home (دینامیک)**: اسکیمای WebSite + Organization + WebPage
- **بلاگ + FAQ (دینامیک)**: اسکیمای Article + FAQPage با سوالات واقعی
- **نمونه بلاگ کامل (دینامیک)**: اسکیمای کامل با نمونه‌های پیش‌فرض

### 📊 داده‌های استفاده شده:
- عنوان مقاله (`data.title`)
- توضیحات متا (`data.meta_description`)
- اسلاگ مقاله (`data.slug`)
- آدرس صفحه (`data.page`)
- تاریخ انتشار (`data.datePublished`)
- سوالات متداول (`faqs`)

## نحوه استفاده

### 1. در صفحه ایجاد/ویرایش مقاله:
1. اطلاعات مقاله را پر کنید (عنوان، توضیحات، سوالات متداول و...)
2. به بخش "اسکیما" بروید
3. روی یکی از دکمه‌های دینامیک (با آیکون 🔧 یا 📝) کلیک کنید
4. اسکیمای کاستوم بر اساس اطلاعات مقاله شما تولید می‌شود

### 2. انواع اسکیماهای دینامیک:

#### صفحات فرزند (Service Schema)
```json
{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Service",
      "name": "عنوان مقاله شما",
      "description": "توضیحات متای شما",
      "provider": {
        "@type": "LocalBusiness",
        "name": "خودراکس",
        "url": "آدرس صفحه شما"
      }
    }
  ]
}
```

#### بلاگ + FAQ (Article + FAQ Schema)
```json
{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Article",
      "headline": "عنوان مقاله شما",
      "description": "توضیحات متای شما",
      "datePublished": "تاریخ انتشار شما"
    },
    {
      "@type": "FAQPage",
      "mainEntity": [
        // سوالات متداول واقعی شما
      ]
    }
  ]
}
```

## مزایا

### ✅ مزایای سیستم دینامیک:
- **دقت بالا**: استفاده از اطلاعات واقعی مقاله
- **صرفه‌جویی در زمان**: عدم نیاز به ویرایش دستی
- **سازگاری SEO**: اسکیماهای بهینه‌شده برای موتورهای جستجو
- **به‌روزرسانی خودکار**: تغییر اطلاعات مقاله، اسکیما را نیز به‌روزرسانی می‌کند

### 🔄 مقایسه با سیستم قدیمی:
| ویژگی | سیستم قدیمی | سیستم دینامیک |
|--------|-------------|----------------|
| عنوان | `[عنوان کامل مقاله]` | `عنوان واقعی مقاله` |
| توضیحات | `[توضیح متا سئو]` | `توضیحات متای واقعی` |
| آدرس | `[صفحه-مقاله]` | `آدرس واقعی صفحه` |
| سوالات | نمونه‌های ثابت | سوالات واقعی مقاله |
| تاریخ | `[تاریخ انتشار]` | `تاریخ انتشار واقعی` |

## نکات مهم

1. **پر کردن اطلاعات**: قبل از تولید اسکیما، حتماً اطلاعات مقاله را کامل پر کنید
2. **سوالات متداول**: برای اسکیماهای FAQ، ابتدا سوالات را اضافه کنید
3. **بررسی نهایی**: پس از تولید، اسکیما را بررسی و در صورت نیاز ویرایش کنید
4. **سازگاری**: سیستم قدیمی همچنان در دسترس است (قسمت "قالب‌های استاتیک")

## پشتیبانی

در صورت بروز مشکل یا نیاز به راهنمایی بیشتر، با تیم توسعه تماس بگیرید.
