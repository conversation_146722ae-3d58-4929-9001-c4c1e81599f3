<div
    class="overflow-hidden rounded-lg bg-white px-5 pt-5 shadow-md max-md:rounded-t-none"
    x-data="{
        collapse: true,
        schema: '',
        setSchema(content) {
            this.schema = content;
            document.getElementById('schema').value = this.schema;
            @this.set('data.schema', this.schema);
        },
        async generateDynamicSchema(type) {
            try {
                const articleData = await @this.get('data');
                const faqs = await @this.get('faqs');
    
                let dynamicSchema = '';
    
                switch (type) {
                    case 'childPage':
                        dynamicSchema = this.generateChildPageSchema(articleData);
                        break;
                    case 'listingPage':
                        dynamicSchema = this.generateListingPageSchema(articleData);
                        break;
                    case 'homePage':
                        dynamicSchema = this.generateHomePageSchema(articleData);
                        break;
                    case 'blogTemplate':
                        dynamicSchema = this.generateBlogTemplateSchema(articleData, faqs);
                        break;
                    case 'blogExample':
                        dynamicSchema = this.generateBlogExampleSchema(articleData, faqs);
                        break;
                }
    
                this.setSchema(dynamicSchema);
            } catch (error) {
                console.error('Error generating schema:', error);
                alert('خطا در تولید اسکیما: ' + error.message);
            }
        },
        generateChildPageSchema(data) {
            const baseUrl = 'https://khodrox.com';
            const pageUrl = data.page ? baseUrl + data.page : baseUrl + '/blog/' + data.slug;
    
            const schema = {
                '@context': 'https://schema.org',
                '@graph': [{
                    '@type': 'Service',
                    'name': data.title || '[نام سرویس]',
                    'description': data.meta_description || data.description || '[توضیح کوتاه سرویس]',
                    'serviceType': '[نوع یا دسته‌بندی سرویس]',
                    'serviceOutput': '[خروجی نهایی/نتیجه سرویس]',
                    'logo': baseUrl + '/logo.png',
                    'provider': {
                        '@type': 'LocalBusiness',
                        'name': 'خودراکس',
                        'telephone': '***********',
                        'url': pageUrl,
                        'address': {
                            '@type': 'PostalAddress',
                            'streetAddress': 'اصفهان،دروازه شیراز',
                            'addressLocality': 'اصفهان',
                            'addressRegion': 'اصفهان',
                            'postalCode': '**********',
                            'addressCountry': 'IR'
                        }
                    },
                    'areaServed': {
                        '@type': 'Place',
                        'address': {
                            '@type': 'PostalAddress',
                            'addressLocality': 'IR',
                            'addressCountry': 'IR'
                        }
                    },
                    'aggregateRating': {
                        '@type': 'AggregateRating',
                        'ratingValue': '4.7',
                        'reviewCount': '134'
                    },
                    'review': [{
                        '@type': 'Review',
                        'author': {
                            '@type': 'Person',
                            'name': 'کاربر ۱'
                        },
                        'reviewBody': 'سرویس دقیق و سریع بود!',
                        'reviewRating': {
                            '@type': 'Rating',
                            'ratingValue': '5'
                        }
                    }, {
                        '@type': 'Review',
                        'author': {
                            '@type': 'Person',
                            'name': 'کاربر ۲'
                        },
                        'reviewBody': 'پشتیبانی عالی و پاسخگو',
                        'reviewRating': {
                            '@type': 'Rating',
                            'ratingValue': '4.5'
                        }
                    }]
                }]
            };
    
            return JSON.stringify(schema, null, 2);
        },
        generateListingPageSchema(data) {
            const baseUrl = 'https://khodrox.com';
            const pageUrl = data.page ? baseUrl + data.page : baseUrl + '/blog/' + data.slug;
    
            const schema = {
                '@context': 'https://schema.org',
                '@graph': [{
                    '@type': 'Service',
                    'name': data.title || '[نام سرویس]',
                    'description': data.meta_description || data.description || '[توضیح کوتاه سرویس]',
                    'serviceType': '[نوع یا دسته‌بندی سرویس]',
                    'serviceOutput': '[خروجی نهایی/نتیجه سرویس]',
                    'logo': baseUrl + '/logo.png',
                    'provider': {
                        '@type': 'LocalBusiness',
                        'name': 'خودراکس',
                        'telephone': '***********',
                        'url': pageUrl,
                        'address': {
                            '@type': 'PostalAddress',
                            'streetAddress': 'اصفهان،دروازه شیراز',
                            'addressLocality': 'اصفهان',
                            'addressRegion': 'اصفهان',
                            'postalCode': '**********',
                            'addressCountry': 'IR'
                        }
                    },
                    'areaServed': {
                        '@type': 'Place',
                        'address': {
                            '@type': 'PostalAddress',
                            'addressLocality': 'IR',
                            'addressCountry': 'IR'
                        }
                    },
                    'aggregateRating': {
                        '@type': 'AggregateRating',
                        'ratingValue': '4.7',
                        'reviewCount': '134'
                    },
                    'review': [{
                        '@type': 'Review',
                        'author': {
                            '@type': 'Person',
                            'name': 'کاربر ۱'
                        },
                        'reviewBody': 'سرویس دقیق و سریع بود!',
                        'reviewRating': {
                            '@type': 'Rating',
                            'ratingValue': '5'
                        }
                    }, {
                        '@type': 'Review',
                        'author': {
                            '@type': 'Person',
                            'name': 'کاربر ۲'
                        },
                        'reviewBody': 'پشتیبانی عالی و پاسخگو',
                        'reviewRating': {
                            '@type': 'Rating',
                            'ratingValue': '4.5'
                        }
                    }]
                }, {
                    '@type': 'ItemList',
                    'name': data.title || '[نام صفحه لیستینگ]',
                    'itemListElement': [{
                        '@type': 'ListItem',
                        'position': 1,
                        'url': '[آدرس صفحه فرزند ۱]'
                    }, {
                        '@type': 'ListItem',
                        'position': 2,
                        'url': '[آدرس صفحه فرزند ۲]'
                    }, {
                        '@type': 'ListItem',
                        'position': 3,
                        'url': '[آدرس صفحه فرزند ۳]'
                    }]
                }]
            };
    
            return JSON.stringify(schema, null, 2);
        },
        generateHomePageSchema(data) {
            const schema = {
                '@context': 'https://schema.org',
                '@graph': [{
                    '@type': 'WebSite',
                    'name': 'خودراکس',
                    'url': 'https://khodrox.com',
                    'potentialAction': {
                        '@type': 'SearchAction',
                        'target': 'https://khodrox.com/search?q={search_term_string}',
                        'query-input': 'required name=search_term_string'
                    },
                    'inLanguage': 'fa-IR'
                }, {
                    '@type': 'Organization',
                    'name': 'خودراکس',
                    'url': 'https://khodrox.com',
                    'logo': 'https://khodrox.com/logo.png',
                    'contactPoint': {
                        '@type': 'ContactPoint',
                        'telephone': '+982191095643',
                        'contactType': 'customer service',
                        'areaServed': 'IR',
                        'knowsLanguage': ['fa', 'en']
                    }
                }, {
                    '@type': 'WebPage',
                    'url': 'https://khodrox.com',
                    'name': data.meta_title || 'خانه | استعلام خودرو، خلافی، پلاک - خودراکس',
                    'description': data.meta_description || 'خودراکس - سامانه‌ی جامع استعلام و پرداخت هوشمند عوارض، جرایم رانندگی، خلافی خودرو و سایر خدمات خودرویی، سریع، امن و مطمئن',
                    'isPartOf': {
                        '@type': 'WebSite',
                        'url': 'https://khodrox.com'
                    },
                    'inLanguage': 'fa-IR'
                }]
            };
    
            return JSON.stringify(schema, null, 2);
        },
        generateBlogTemplateSchema(data, faqs) {
            const baseUrl = 'https://khodrox.com';
            const pageUrl = data.page ? baseUrl + data.page : baseUrl + '/blog/' + data.slug;
            const currentDate = data.datePublished || new Date().toISOString().split('T')[0];
    
            const schema = {
                '@context': 'https://schema.org',
                '@graph': [{
                    '@type': 'Article',
                    '@id': pageUrl + '#article',
                    'headline': data.title || '[عنوان کامل مقاله]',
                    'description': data.meta_description || '[توضیح متا سئو]',
                    'image': baseUrl + '/logo.webp',
                    'author': {
                        '@type': 'Organization',
                        'name': 'خودراکس',
                        'url': baseUrl
                    },
                    'publisher': {
                        '@type': 'Organization',
                        'name': 'خودراکس',
                        'logo': {
                            '@type': 'ImageObject',
                            'url': baseUrl + '/logo.webp'
                        }
                    },
                    'datePublished': currentDate,
                    'dateModified': currentDate,
                    'mainEntityOfPage': pageUrl
                }]
            };
    
            if (faqs && faqs.length > 0) {
                const faqSchema = {
                    '@type': 'FAQPage',
                    '@id': pageUrl + '#faq',
                    'mainEntity': faqs.map(faq => ({
                        '@type': 'Question',
                        'name': faq.question || '[سؤال]',
                        'acceptedAnswer': {
                            '@type': 'Answer',
                            'text': faq.answer || '[پاسخ]'
                        }
                    }))
                };
                schema['@graph'].push(faqSchema);
            }
    
            return JSON.stringify(schema, null, 2);
        },
        generateBlogExampleSchema(data, faqs) {
            const baseUrl = 'https://khodrox.com';
            const pageUrl = data.page ? baseUrl + data.page : baseUrl + '/blog/' + data.slug;
            const currentDate = data.datePublished || new Date().toISOString().split('T')[0];
    
            const schema = {
                '@context': 'https://schema.org',
                '@graph': [{
                    '@type': 'Article',
                    '@id': pageUrl + '#article',
                    'mainEntityOfPage': pageUrl,
                    'headline': data.title || 'استعلام خلافی دولتی',
                    'description': data.meta_description || 'در این مقاله از خودراکس، با روش‌های مطمئن و رسمی برای استعلام خلافی دولتی خودرو آشنا شوید و از جریمه‌های رانندگی به‌موقع مطلع شوید.',
                    'image': baseUrl + '/logo.webp',
                    'author': {
                        '@type': 'Organization',
                        'name': 'خودراکس',
                        'url': baseUrl
                    },
                    'publisher': {
                        '@type': 'Organization',
                        'name': 'خودراکس',
                        'logo': {
                            '@type': 'ImageObject',
                            'url': baseUrl + '/logo.webp'
                        }
                    },
                    'datePublished': currentDate,
                    'dateModified': currentDate
                }]
            };
    
            const faqData = faqs && faqs.length > 0 ? faqs : [{
                question: 'استعلام خلافی دولتی چیست؟',
                answer: 'استعلام خلافی دولتی یعنی دریافت اطلاعات رسمی و دقیق از میزان جرایم رانندگی ثبت‌شده در سامانه راهور ناجا، بدون واسطه و از مراجع معتبر.'
            }, {
                question: 'از کجا می‌توان خلافی دولتی خودرو را دریافت کرد؟',
                answer: 'از طریق سامانه خودراکس، اپلیکیشن پلیس راهور، سایت راهور ۱۲۰ یا مراجعه حضوری به دفاتر پلیس +۱۰ می‌توانید خلافی دولتی خودرو را استعلام بگیرید.'
            }];
    
            const faqSchema = {
                '@type': 'FAQPage',
                '@id': pageUrl + '#faq',
                'mainEntity': faqData.map(faq => ({
                    '@type': 'Question',
                    'name': faq.question,
                    'acceptedAnswer': {
                        '@type': 'Answer',
                        'text': faq.answer
                    }
                }))
            };
            schema['@graph'].push(faqSchema);
    
            return JSON.stringify(schema, null, 2);
        }"
    x-transition:enter=" duration-300 ease-out"
    x-transition:leave=" duration-300 ease-in"
    :class="collapse == false ? 'h-16' : 'p-8'"
>
    <div
        class="mb-4 flex items-center justify-between border-b-2 border-dashed pb-3"
        :class="collapse ? 'border-gray-200' : 'border-white'"
    >
        <h1 class="text-lg font-bold text-gray-700 max-md:text-base">اسکیما</h1>
        <button
            class="flex h-7 w-7 items-center justify-center rounded-full transition-all hover:bg-gray-100"
            type="button"
            @click="collapse = !collapse"
        >
            <svg
                class="size-5 text-gray-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                />
            </svg>
        </button>
    </div>
    <div class="mb-2 mt-2 flex flex-wrap items-center gap-3">
        <button
            class="flex items-center justify-center rounded-xl bg-gray-100 px-3 py-1 text-sm"
            type="button"
            @click="setSchema('')"
        >
            خالی کن
        </button>
        <button
            class="flex items-center justify-center rounded-xl bg-blue-100 px-3 py-1 text-sm transition-colors hover:bg-blue-200"
            type="button"
            @click="generateDynamicSchema('childPage')"
        >
            🔧 صفحات فرزند (دینامیک)
        </button>
        <button
            class="flex items-center justify-center rounded-xl bg-blue-100 px-3 py-1 text-sm transition-colors hover:bg-blue-200"
            type="button"
            @click="generateDynamicSchema('listingPage')"
        >
            🔧 صفحات لیستینگ (دینامیک)
        </button>
        <button
            class="flex items-center justify-center rounded-xl bg-blue-100 px-3 py-1 text-sm transition-colors hover:bg-blue-200"
            type="button"
            @click="generateDynamicSchema('homePage')"
        >
            🔧 صفحه home (دینامیک)
        </button>
        <button
            class="flex items-center justify-center rounded-xl bg-green-100 px-3 py-1 text-sm transition-colors hover:bg-green-200"
            type="button"
            @click="generateDynamicSchema('blogTemplate')"
        >
            📝 بلاگ + FAQ (دینامیک)
        </button>
        <button
            class="flex items-center justify-center rounded-xl bg-green-100 px-3 py-1 text-sm transition-colors hover:bg-green-200"
            type="button"
            @click="generateDynamicSchema('blogExample')"
        >
            📝 نمونه بلاگ کامل (دینامیک)
        </button>

        <!-- دکمه‌های استاتیک قدیمی -->
        <div class="my-2 w-full border-t border-gray-200"></div>
        <p class="mb-2 text-xs text-gray-500">قالب‌های استاتیک (قدیمی):</p>
        <button
            class="flex items-center justify-center rounded-xl bg-gray-100 px-3 py-1 text-sm"
            type="button"
            @click="setSchema(window.schemas.childPage)"
        >
            صفحات فرزند یا تک صفحه ای
        </button>
        <button
            class="flex items-center justify-center rounded-xl bg-gray-100 px-3 py-1 text-sm"
            type="button"
            @click="setSchema(window.schemas.listingPage)"
        >
            صفحات لیستینگ(مادر)
        </button>
        <button
            class="flex items-center justify-center rounded-xl bg-gray-100 px-3 py-1 text-sm"
            type="button"
            @click="setSchema(window.schemas.homePage)"
        >
            صفحه ی home
        </button>
        <button
            class="flex items-center justify-center rounded-xl bg-gray-100 px-3 py-1 text-sm"
            type="button"
            @click="setSchema(window.schemas.blogTemplate)"
        >
            قالب خام اسکیمای بلاگ JSON-LD
        </button>
        <button
            class="flex items-center justify-center rounded-xl bg-gray-100 px-3 py-1 text-sm"
            type="button"
            @click="setSchema(window.schemas.blogExample)"
        >
            نمونه بلاگ (article + FAQ)
        </button>
    </div>
    <div>
        <textarea
            class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2.5 text-sm text-gray-900 focus:border-red-500 focus:ring-red-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-500 dark:focus:ring-red-500"
            id="schema"
            rows="60"
            dir="ltr"
            wire:model="data.schema"
            placeholder=""
        ></textarea>
    </div>
    <script>
        window.schemas = {
            childPage: `{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Service",
      "name": "[Dynamic: Service Landing Page Name]",
      "description": "[Dynamic: Short description of the service]",
      "serviceType": "[Dynamic: Type or category of service]",
      "serviceOutput": "[Dynamic: Final output/result of the service]",
      "logo": "https://khodrox.com/logo.png",
      "provider": {
        "@type": "LocalBusiness",
        "name": "خودراکس",
        "telephone": "***********",
        "url": "[Dynamic: Landing Page URL]",
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "اصفهان،دروازه شیراز",
          "addressLocality": "اصفهان",
          "addressRegion": "اصفهان",
          "postalCode": "**********",
          "addressCountry": "IR"
        }
      },
      "areaServed": {
        "@type": "Place",
        "address": {
          "@type": "PostalAddress",
          "addressLocality": "IR",
          "addressCountry": "IR"
        }
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.7",
        "reviewCount": "134"
      },
      "review": [
        {
          "@type": "Review",
          "author": {
            "@type": "Person",
            "name": "کاربر ۱"
          },
          "reviewBody": "سرویس دقیق و سریع بود!",
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": "5"
          }
        },
        {
          "@type": "Review",
          "author": {
            "@type": "Person",
            "name": "کاربر ۲"
          },
          "reviewBody": "پشتیبانی عالی و پاسخگو",
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": "4.5"
          }
        }
      ]
    }
  ]
}`,
            listingPage: `{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Service",
      "name": "[Dynamic: Service Landing Page Name]",
      "description": "[Dynamic: Short description of the service]",
      "serviceType": "[Dynamic: Type or category of service]",
      "serviceOutput": "[Dynamic: Final output/result of the service]",
      "logo": "https://khodrox.com/logo.png",
      "provider": {
        "@type": "LocalBusiness",
        "name": "خودراکس",
        "telephone": "***********",
        "url": "[Dynamic: Landing Page URL]",
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "اصفهان،دروازه شیراز",
          "addressLocality": "اصفهان",
          "addressRegion": "اصفهان",
          "postalCode": "**********",
          "addressCountry": "IR"
        }
      },
      "areaServed": {
        "@type": "Place",
        "address": {
          "@type": "PostalAddress",
          "addressLocality": "IR",
          "addressCountry": "IR"
        }
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.7",
        "reviewCount": "134"
      },
      "review": [
        {
          "@type": "Review",
          "author": {
            "@type": "Person",
            "name": "کاربر ۱"
          },
          "reviewBody": "سرویس دقیق و سریع بود!",
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": "5"
          }
        },
        {
          "@type": "Review",
          "author": {
            "@type": "Person",
            "name": "کاربر ۲"
          },
          "reviewBody": "پشتیبانی عالی و پاسخگو",
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": "4.5"
          }
        }
      ]
    },
    {
      "@type": "ItemList",
      "name": "[Dynamic: Service Landing Page Name]",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "url": "[Dynamic: Child Page URL 1]"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "url": "[Dynamic: Child Page URL 2]"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "url": "[Dynamic: Child Page URL 3]"
        }
      ]
    }
  ]
}`,
            homePage: `{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "WebSite",
      "name": "خودراکس",
      "url": "https://khodrox.com",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://khodrox.com/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      },
      "inLanguage": "fa-IR"
    },
    {
      "@type": "Organization",
      "name": "خودراکس",
      "url": "https://khodrox.com",
      "logo": "https://khodrox.com/logo.png",
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+982191095643",
        "contactType": "customer service",
        "areaServed": "IR",
        "knowsLanguage": ["fa", "en"]
      }
    },
    {
      "@type": "WebPage",
      "url": "https://khodrox.com",
      "name": "خانه | استعلام خودرو، خلافی، پلاک - خودراکس",
      "description": "خودراکس - سامانه‌ی جامع استعلام و پرداخت هوشمند عوارض، جرایم رانندگی، خلافی خودرو و سایر خدمات خودرویی، سریع، امن و مطمئن",
      "isPartOf": {
        "@type": "WebSite",
        "url": "https://khodrox.com"
      },
      "inLanguage": "fa-IR"
    }
  ]
}`,
            blogTemplate: `{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Article",
      "@id": "[صفحه-مقاله]#article",
      "headline": "[عنوان کامل مقاله]",
      "description": "[توضیح متا سئو]",
      "image": "[آدرس تصویر لوگو یا کاور]",
      "author": {
        "@type": "Organization",
        "name": "[نام برند یا نویسنده]",
        "url": "[آدرس برند]"
      },
      "publisher": {
        "@type": "Organization",
        "name": "[نام برند]",
        "logo": {
          "@type": "ImageObject",
          "url": "[آدرس لوگو]"
        }
      },
      "datePublished": "[تاریخ انتشار]",
      "dateModified": "[تاریخ ویرایش]"
    },
    {
      "@type": "FAQPage",
      "@id": "[صفحه-مقاله]#faq",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "[سؤال شماره ۱]",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "[پاسخ سؤال ۱]"
          }
        },
        {
          "@type": "Question",
          "name": "[سؤال شماره ۲]",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "[پاسخ سؤال ۲]"
          }
        }
      ]
    }
  ]
}`,
            blogExample: `{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Article",
      "@id": "https://khodrox.com/blog/استعلام-خلافی-دولتی#article",
      "mainEntityOfPage": "https://khodrox.com/blog/استعلام-خلافی-دولتی",
      "headline": "استعلام خلافی دولتی",
      "description": "در این مقاله از خودراکس، با روش‌های مطمئن و رسمی برای استعلام خلافی دولتی خودرو آشنا شوید و از جریمه‌های رانندگی به‌موقع مطلع شوید.",
      "image": "https://khodrox.com/logo.webp",
      "author": {
        "@type": "Organization",
        "name": "خودراکس",
        "url": "https://khodrox.com"
      },
      "publisher": {
        "@type": "Organization",
        "name": "خودراکس",
        "logo": {
          "@type": "ImageObject",
          "url": "https://khodrox.com/logo.webp"
        }
      },
      "datePublished": "2025-07-13",
      "dateModified": "2025-07-13"
    },
    {
      "@type": "FAQPage",
      "@id": "https://khodrox.com/blog/استعلام-خلافی-دولتی#faq",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "استعلام خلافی دولتی چیست؟",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "استعلام خلافی دولتی یعنی دریافت اطلاعات رسمی و دقیق از میزان جرایم رانندگی ثبت‌شده در سامانه راهور ناجا، بدون واسطه و از مراجع معتبر."
          }
        },
        {
          "@type": "Question",
          "name": "از کجا می‌توان خلافی دولتی خودرو را دریافت کرد؟",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "از طریق سامانه خودراکس، اپلیکیشن پلیس راهور، سایت راهور ۱۲۰ یا مراجعه حضوری به دفاتر پلیس +۱۰ می‌توانید خلافی دولتی خودرو را استعلام بگیرید."
          }
        }
      ]
    }
  ]
}`
        };
    </script>
</div>
