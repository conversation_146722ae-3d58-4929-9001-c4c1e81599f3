<div class="min-h-screen p-6">
    <div>
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-xl font-bold text-gray-900">پروفایل کاربری</h1>
            <p class="mt-2 text-sm text-gray-600">مدیریت اطلاعات شخصی و تنظیمات حساب کاربری</p>
        </div>

        <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
            <!-- Profile Picture Section -->
            <div class="lg:col-span-1">
                <div class="rounded-lg bg-white p-6 shadow">
                    <h2 class="mb-4 text-lg font-semibold text-gray-900">تصویر پروفایل</h2>

                    <div class="flex flex-col items-center">
                        <!-- Current Avatar -->
                        <div class="relative mb-4">
                            <div
                                class="cursor-pointer"
                                onclick="document.getElementById('avatarInput').click()"
                            >
                                @if ($current_avatar)
                                    <img
                                        class="h-32 w-32 rounded-full object-cover ring-4 ring-blue-100 transition-all duration-200 hover:ring-blue-300"
                                        src="{{ $current_avatar }}"
                                        alt="تصویر پروفایل"
                                    >
                                @else
                                    <div
                                        class="flex h-32 w-32 items-center justify-center rounded-full bg-gray-200 ring-4 ring-gray-100 transition-all duration-200 hover:ring-blue-300">
                                        <svg
                                            class="h-16 w-16 text-gray-400"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                                clip-rule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                @endif

                                <!-- Upload overlay -->
                                <div
                                    class="absolute inset-0 flex items-center justify-center rounded-full bg-black bg-opacity-0 transition-all duration-200 hover:bg-opacity-50">
                                    <svg
                                        class="h-8 w-8 text-white opacity-0 transition-opacity duration-200 group-hover:opacity-100"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                                        ></path>
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                                        ></path>
                                    </svg>
                                </div>
                            </div>

                            <!-- Loading overlay for upload -->
                            @if ($uploading)
                                <div
                                    class="absolute inset-0 flex items-center justify-center rounded-full bg-black bg-opacity-50">
                                    <svg
                                        class="h-8 w-8 animate-spin text-white"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                    >
                                        <circle
                                            class="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            stroke-width="4"
                                        ></circle>
                                        <path
                                            class="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                        ></path>
                                    </svg>
                                </div>
                            @endif
                        </div>

                        <!-- Hidden File Input -->
                        <input
                            class="hidden"
                            id="avatarInput"
                            type="file"
                            wire:model="avatar"
                            accept="image/*"
                            @if ($uploading) disabled @endif
                        >

                        @error('avatar')
                            <span class="mt-1 text-sm text-red-500">{{ $message }}</span>
                        @enderror

                        <!-- Remove Avatar Button -->
                        @if ($current_avatar)
                            <button
                                class="mt-4 flex items-center text-sm text-red-600 hover:text-red-800 disabled:cursor-not-allowed disabled:opacity-50"
                                type="button"
                                wire:click="removeAvatar"
                                @if ($removing) disabled @endif
                            >
                                @if ($removing)
                                    <svg
                                        class="ml-2 h-4 w-4 animate-spin"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                    >
                                        <circle
                                            class="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            stroke-width="4"
                                        ></circle>
                                        <path
                                            class="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                        ></path>
                                    </svg>
                                    در حال حذف...
                                @else
                                    حذف تصویر پروفایل
                                @endif
                            </button>
                        @endif
                    </div>
                </div>

                <!-- User Info (Read-only) -->
                <div class="mt-6 rounded-lg bg-white p-6 shadow">
                    <h2 class="mb-4 text-lg font-semibold text-gray-900">اطلاعات حساب</h2>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">شماره موبایل</label>
                            <div class="mt-1 rounded-md bg-gray-50 px-3 py-2 text-gray-900">
                                {{ $phone }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">سطح دسترسی</label>
                            <div class="mt-1 rounded-md bg-gray-50 px-3 py-2 text-gray-900">
                                {{ $roles }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">منبع</label>
                            <div class="mt-1 rounded-md bg-gray-50 px-3 py-2 text-gray-900">
                                پنل مدیریت
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Form Section -->
            <div class="lg:col-span-2">
                <form
                    class="rounded-lg bg-white p-6 shadow"
                    wire:submit.prevent="save"
                >
                    <h2 class="mb-6 text-lg font-semibold text-gray-900">اطلاعات شخصی</h2>

                    <!-- Full Name -->
                    <div class="mb-6">
                        <label
                            class="block text-sm font-medium text-gray-700"
                            for="fullname"
                        >
                            نام و نام خانوادگی
                        </label>
                        <input
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            id="fullname"
                            type="text"
                            wire:model="fullname"
                            required
                        >
                        @error('fullname')
                            <span class="mt-1 text-sm text-red-500">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- About -->
                    <div class="mt-6">
                        <label
                            class="block text-sm font-medium text-gray-700"
                            for="about"
                        >
                            درباره من
                        </label>
                        <textarea
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            id="about"
                            wire:model="about"
                            rows="4"
                            placeholder="توضیحی درباره خودتان بنویسید..."
                        ></textarea>
                        @error('about')
                            <span class="mt-1 text-sm text-red-500">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Submit Button -->
                    <div class="mt-8 flex justify-end">
                        <button
                            class="flex items-center rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            type="submit"
                            @if ($saving) disabled @endif
                        >
                            @if ($saving)
                                <svg
                                    class="ml-2 h-4 w-4 animate-spin"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                >
                                    <circle
                                        class="opacity-25"
                                        cx="12"
                                        cy="12"
                                        r="10"
                                        stroke="currentColor"
                                        stroke-width="4"
                                    ></circle>
                                    <path
                                        class="opacity-75"
                                        fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    ></path>
                                </svg>
                                در حال ذخیره...
                            @else
                                ذخیره تغییرات
                            @endif
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
