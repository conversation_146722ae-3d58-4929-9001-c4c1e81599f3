<!DOCTYPE html>
<html lang="fa">

    <head>
        <meta charset="UTF-8">
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0"
        >
        <meta
            http-equiv="X-UA-Compatible"
            content="ie=edge"
        >
        <meta
            name="theme-color"
            content="#343236"
        >
        <meta
            NAME="robots"
            CONTENT="noindex,nofollow"
        >
        <meta
            name="csrf-token"
            content="{{ csrf_token() }}"
        />

        <title>ایرانی سافت @yield('title')</title>
        @csrf

        @vite(['resources/css/app.css', 'resources/js/app.js'])
        @if (app()->isProduction())
            @vite('resources/js/livewire_handle_error.js')
        @endif
        <link
            type="text/css"
            href="/assets/css/jalalidatepicker.min.css"
            rel="stylesheet"
        />
        @stack('style')

        <script src="/assets/js/chart.js"></script>

        @livewireStyles
    </head>

    <body
        class="m-0 flex items-start p-0"
        x-data
        :class="{ 'overflow-hidden': $store.lock.on }"
    >
        <main
            class="h-full w-full"
            x-data="{
                sidebarOpen: JSON.parse(localStorage.getItem('sidebarOpen')) ?? false,
                toggleSidebar() {
                    this.sidebarOpen = !this.sidebarOpen;
                    localStorage.setItem('sidebarOpen', JSON.stringify(this.sidebarOpen));
                }
            }"
        >

            <!-- Sidebar -->
            <div
                class="fixed right-0 top-0 z-[999] h-screen bg-white shadow transition-all duration-150 max-md:hidden"
                :class="sidebarOpen ? 'w-80' : 'w-20'"
            >

                <!-- Header -->
                <div :class="sidebarOpen ? 'px-6 py-6' : 'py-3'">
                    <div :class="sidebarOpen ? 'flex items-center gap-3' : ' flex flex-col gap-2 flex-cols-reverse'">
                        <button
                            class="text-gray-500 transition-all hover:text-gray-700"
                            type="button"
                            :class="!sidebarOpen ? 'w-full flex items-center justify-center' : 'absolute left-2 top-2'"
                            @click="toggleSidebar()"
                        >
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M3.75 6.75h16.5M3.75 12h16.5M12 17.25h8.25"
                                />
                            </svg>

                        </button>
                        <div
                            class="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-blue-500 to-purple-600"
                            :class="!sidebarOpen ? 'mx-auto' : ''"
                        >
                            <svg
                                class="h-7 w-7 text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"
                                ></path>
                                <path
                                    d="M3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0z"
                                ></path>
                            </svg>
                        </div>
                        <div>
                            <div class="flex items-center justify-between">
                                <h1
                                    class="text-xl font-bold text-gray-900"
                                    x-show="sidebarOpen"
                                >ایرانی سافت</h1>

                            </div>
                            <p
                                class="text-sm text-gray-400"
                                x-show="sidebarOpen"
                            >
                                کنترل کامل سیستم در یک نگاه
                            </p>

                        </div>
                    </div>

                </div>

                <!-- Navigation Menu -->
                <nav
                    class="relative"
                    :class="sidebarOpen ? 'px-4 py-6' : ''"
                >

                    <ul>
                        <!-- Dashboard -->
                        <li class="border-b border-gray-200">

                            <a
                                class="group relative flex items-center gap-3 rounded-lg px-4 py-3 text-gray-700 transition-all hover:bg-gray-100"
                                href="{{ auth()->user()?->roles()?->first()?->redirectPath() ?? '/' }}"
                                :class="!sidebarOpen ? ' items-center justify-center' : ''"
                                x-data="{ tooltip: false }"
                                @mouseenter="tooltip = true"
                                @mouseleave="tooltip = false"
                            >
                                <div>
                                    <svg
                                        class="size-6 text-gray-500"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"
                                        />
                                    </svg>

                                </div>
                                <span
                                    class="text-base font-medium"
                                    x-show="sidebarOpen"
                                >داشبورد من</span>
                                <div
                                    class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                    x-show="tooltip && !sidebarOpen"
                                    x-transition:enter="transition ease-out duration-200"
                                    x-transition:enter-start="opacity-0 translate-x-2"
                                    x-transition:enter-end="opacity-100 translate-x-0"
                                    x-transition:leave="transition ease-in duration-150"
                                    x-transition:leave-start="opacity-100 translate-x-0"
                                    x-transition:leave-end="opacity-0 translate-x-2"
                                >
                                    داشبورد من
                                </div>
                            </a>
                        </li>

                        @can('show-list-users')
                            <!-- Users -->
                            <li class="border-b border-gray-200">
                                <a
                                    class="group relative flex items-center gap-3 rounded-lg px-4 py-3 text-gray-700 transition-all hover:bg-gray-100"
                                    href="/dashboard/users"
                                    x-data="{ tooltip: false }"
                                    @mouseenter="tooltip = true"
                                    @mouseleave="tooltip = false"
                                    :class="!sidebarOpen ? ' items-center justify-center' : ''"
                                >
                                    <div class="">
                                        <svg
                                            class="size-6 text-gray-500"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                                            />
                                        </svg>

                                    </div>
                                    <span
                                        class="text-base font-medium"
                                        x-show="sidebarOpen"
                                    >لیست کاربران</span>
                                    <div
                                        class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                        x-show="tooltip && !sidebarOpen"
                                        x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 translate-x-2"
                                        x-transition:enter-end="opacity-100 translate-x-0"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 translate-x-0"
                                        x-transition:leave-end="opacity-0 translate-x-2"
                                    >
                                        لیست کاربران
                                    </div>
                                </a>
                            </li>
                        @endcan

                        <!-- Inquiries Section -->
                        @canany(['show-history-inquries-car-motor', 'driving-license-demerit-points-history',
                            'show-history-inquries-shebacart'])

                            <li
                                class="border-b border-gray-200"
                                x-data="{ open: false }"
                            >
                                <button
                                    class="group relative flex w-full rounded-lg px-4 py-3 text-gray-700 transition-all hover:bg-gray-100"
                                    :class="!sidebarOpen ? ' items-center justify-center' : ' items-center justify-between'"
                                    @click="open = !open"
                                    x-data="{ tooltip: false }"
                                    @mouseenter="tooltip = true"
                                    @mouseleave="tooltip = false"
                                >
                                    <div class="flex items-center gap-3">
                                        <div class="">
                                            <svg
                                                class="size-6 text-gray-500"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5m.75-9 3-3 2.148 2.148A12.061 12.061 0 0 1 16.5 7.605"
                                                />
                                            </svg>

                                        </div>
                                        <span
                                            class="text-base font-medium"
                                            x-show="sidebarOpen"
                                        >تاریخچه سرویس ها</span>
                                    </div>
                                    <svg
                                        class="h-4 w-4 transition-transform"
                                        x-show="sidebarOpen"
                                        :class="{ 'rotate-180': open }"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                    >
                                        <path
                                            fill-rule="evenodd"
                                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                            clip-rule="evenodd"
                                        ></path>
                                    </svg>
                                    <div
                                        class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                        x-show="tooltip && !sidebarOpen"
                                        x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 translate-x-2"
                                        x-transition:enter-end="opacity-100 translate-x-0"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 translate-x-0"
                                        x-transition:leave-end="opacity-0 translate-x-2"
                                    >
                                        استعلامات خودرو و موتور
                                    </div>
                                </button>
                                <div
                                    class="space-y-1 bg-gray-50 p-3"
                                    style="display: none;"
                                    x-show="open"
                                    x-transition=""
                                >
                                    @can('show-history-inquries-car-motor')
                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/inquiries-cart-motor"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >

                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <svg
                                                class="size-6 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"
                                                />
                                            </svg>

                                            <span x-show="sidebarOpen">استعلامات خودرو و موتور</span>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                استعلامات خودرو و موتور
                                            </div>
                                        </a>
                                    @endcan
                                    @can('driving-license-demerit-points-history')
                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/inquiries-negative-score"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >

                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <span x-show="sidebarOpen">استعلام نمره منفی گواهینامه</span>
                                            <svg
                                                class="size-6 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"
                                                />
                                            </svg>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                استعلام نمره منفی گواهینامه
                                            </div>
                                        </a>

                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/inquiries-driving-license-status"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >
                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <span x-show="sidebarOpen">استعلام وضعیت گواهینامه</span>
                                            <svg
                                                class="size-6 shrink-0 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"
                                                />
                                            </svg>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                استعلام وضعیت گواهینامه
                                            </div>
                                        </a>

                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/inquiries-carid-documents"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >
                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <span x-show="sidebarOpen">استعلام وضعیت کارت و سند خودرو</span>
                                            <svg
                                                class="size-6 shrink-0 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"
                                                />
                                            </svg>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                استعلام وضعیت کارت و سند خودرو
                                            </div>
                                        </a>
                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/inquiries-insurance-third-party"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >
                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <span x-show="sidebarOpen">استعلام بیمه شخص ثالث</span>
                                            <svg
                                                class="size-6 shrink-0 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"
                                                />
                                            </svg>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                استعلام بیمه شخص ثالث
                                            </div>
                                        </a>
                                    @endcan
                                    @can('show-history-inquries-shebacart')
                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/inquiries-shebacart-cart-motor"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >
                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <span x-show="sidebarOpen">شباکارت - خودرو و موتور</span>
                                            <svg
                                                class="size-6 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                />
                                            </svg>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                شباکارت - خودرو و موتور
                                            </div>
                                        </a>

                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/inquiries-shebacart-financial"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >
                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <span x-show="sidebarOpen">شباکارت - استعلام های بانکی</span>
                                            <svg
                                                class="size-6 shrink-0 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"
                                                />
                                            </svg>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                شباکارت - استعلام های بانکی
                                            </div>
                                        </a>
                                    @endcan
                                </div>
                            </li>
                        @endcan

                        <!-- Financial Reports Section -->
                        @canany(['show-report-transaction-site', 'show-report-transaction-zarinpal-paid',
                            'show-report-transaction-zarinpal-refund', 'show-report-transaction-refund',
                            'show-report-transaction-qabzino'])
                            <li
                                class="border-b border-gray-200"
                                x-data="{ open: false }"
                            >
                                <button
                                    class="group relative flex w-full rounded-lg px-4 py-3 text-gray-700 transition-all hover:bg-gray-100"
                                    :class="!sidebarOpen ? ' items-center justify-center' : ' items-center justify-between'"
                                    @click="open = !open"
                                    x-data="{ tooltip: false }"
                                    @mouseenter="tooltip = true"
                                    @mouseleave="tooltip = false"
                                >
                                    <div class="flex items-center gap-3">
                                        <div class="">
                                            <svg
                                                class="size-6 text-gray-500"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"
                                                />
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"
                                                />
                                            </svg>

                                        </div>
                                        <span
                                            class="text-base font-medium"
                                            x-show="sidebarOpen"
                                        >گزارشات مالی سامانه</span>
                                    </div>
                                    <svg
                                        class="h-4 w-4 transition-transform"
                                        x-show="sidebarOpen"
                                        :class="{ 'rotate-180': open }"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                    >
                                        <path
                                            fill-rule="evenodd"
                                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                            clip-rule="evenodd"
                                        ></path>
                                    </svg>
                                    <div
                                        class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                        x-show="tooltip && !sidebarOpen"
                                        x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 translate-x-2"
                                        x-transition:enter-end="opacity-100 translate-x-0"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 translate-x-0"
                                        x-transition:leave-end="opacity-0 translate-x-2"
                                    >
                                        گزارشات مالی سامانه
                                    </div>
                                </button>
                                <div
                                    class="space-y-1 bg-gray-50 p-3"
                                    style="display: none;"
                                    x-show="open"
                                    x-transition=""
                                >
                                    @can('show-report-transaction-site')
                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/transactions"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >
                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <span x-show="sidebarOpen">لیست تراکنش های سامانه</span>
                                            <svg
                                                class="size-6 shrink-0 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                />
                                            </svg>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                لیست تراکنش های سامانه
                                            </div>
                                        </a>
                                    @endcan
                                    @can('show-report-transaction-zarinpal-paid')
                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/transactions/zarinpal"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >
                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <span x-show="sidebarOpen">تراکنش های موفق زرین پال</span>
                                            <svg
                                                class="size-6 shrink-0 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="m7.875 14.25 1.214 1.942a2.25 2.25 0 0 0 1.908 1.058h2.006c.776 0 1.497-.4 1.908-1.058l1.214-1.942M2.41 9h4.636a2.25 2.25 0 0 1 1.872 1.002l.164.246a2.25 2.25 0 0 0 1.872 1.002h2.092a2.25 2.25 0 0 0 1.872-1.002l.164-.246A2.25 2.25 0 0 1 16.954 9h4.636M2.41 9a2.25 2.25 0 0 0-.16.832V12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 12V9.832c0-.287-.055-.57-.16-.832M2.41 9a2.25 2.25 0 0 1 .382-.632l3.285-3.832a2.25 2.25 0 0 1 1.708-.786h8.43c.657 0 1.281.287 1.709.786l3.284 3.832c.163.19.291.404.382.632M4.5 20.25h15A2.25 2.25 0 0 0 21.75 18v-2.625c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125V18a2.25 2.25 0 0 0 2.25 2.25Z"
                                                />
                                            </svg>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                تراکنش های موفق زرین پال
                                            </div>
                                        </a>
                                    @endcan
                                    @can('show-report-transaction-zarinpal-refund')
                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/transactions/zarinpal/refund"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >
                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <span x-show="sidebarOpen">تراکنش های برگشتی زرین پال</span>
                                            <svg
                                                class="size-6 shrink-0 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
                                                />
                                            </svg>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                تراکنش های موفق زرین پال
                                            </div>
                                        </a>
                                    @endcan
                                    @can('show-report-transaction-refund')
                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/refund"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >
                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <span x-show="sidebarOpen">لیست تراکنش های برگشتی</span>
                                            <svg
                                                class="size-6 shrink-0 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M7.5 7.5h-.75A2.25 2.25 0 0 0 4.5 9.75v7.5a2.25 2.25 0 0 0 2.25 2.25h7.5a2.25 2.25 0 0 0 2.25-2.25v-7.5a2.25 2.25 0 0 0-2.25-2.25h-.75m0-3-3-3m0 0-3 3m3-3v11.25m6-2.25h.75a2.25 2.25 0 0 1 2.25 2.25v7.5a2.25 2.25 0 0 1-2.25 2.25h-7.5a2.25 2.25 0 0 1-2.25-2.25v-.75"
                                                />
                                            </svg>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                لیست تراکنش های برگشتی
                                            </div>
                                        </a>
                                    @endcan
                                    @can('show-report-transaction-qabzino')
                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/transactions/qabzino"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >
                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <svg
                                                class="size-6 shrink-0 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"
                                                />
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"
                                                />
                                            </svg>

                                            <span x-show="sidebarOpen">تراکنش های پرداختی قبضینو</span>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                تراکنش های پرداختی قبضینو
                                            </div>
                                        </a>
                                        <a
                                            class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                            href="/dashboard/transactions/qabzino"
                                            x-data="{ tooltip: false }"
                                            @mouseenter="tooltip = true"
                                            @mouseleave="tooltip = false"
                                        >
                                            <span
                                                class="text-gray-300"
                                                x-show="sidebarOpen"
                                            >-</span>
                                            <svg
                                                class="size-6 shrink-0 text-gray-500"
                                                x-show="!sidebarOpen"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"
                                                />
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"
                                                />
                                            </svg>

                                            <span x-show="sidebarOpen">تیکت ها</span>
                                            <div
                                                class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                                x-show="tooltip && !sidebarOpen"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 translate-x-2"
                                                x-transition:enter-end="opacity-100 translate-x-0"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 translate-x-0"
                                                x-transition:leave-end="opacity-0 translate-x-2"
                                            >
                                                تیکت ها
                                            </div>
                                        </a>
                                    @endcan
                                </div>
                            </li>
                        @endcanany
                        <!-- SMS -->
                        @can('show-payamak')
                            <li class="border-b border-gray-200">
                                <a
                                    class="group relative flex items-center gap-3 rounded-lg px-4 py-3 text-gray-700 transition-all hover:bg-gray-100"
                                    href="/dashboard/sms"
                                    x-data="{ tooltip: false }"
                                    @mouseenter="tooltip = true"
                                    @mouseleave="tooltip = false"
                                    :class="!sidebarOpen ? ' items-center justify-center' : ''"
                                >
                                    <div class="">
                                        <svg
                                            class="size-6 text-gray-500"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"
                                            />
                                        </svg>

                                    </div>
                                    <span
                                        class="text-base font-medium"
                                        x-show="sidebarOpen"
                                    >لیست پیامک های ارسالی</span>
                                    <div
                                        class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                        x-show="tooltip && !sidebarOpen"
                                        x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 translate-x-2"
                                        x-transition:enter-end="opacity-100 translate-x-0"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 translate-x-0"
                                        x-transition:leave-end="opacity-0 translate-x-2"
                                    >
                                        لیست پیامک های ارسالی
                                    </div>
                                </a>
                            </li>
                        @endcan

                        <!-- Blog Section -->
                        @canany(['show-articles'])
                            <li
                                class="border-b border-gray-200"
                                x-data="{ open: false }"
                            >
                                <button
                                    class="group relative flex w-full rounded-lg px-4 py-3 text-gray-700 transition-all hover:bg-gray-100"
                                    :class="!sidebarOpen ? ' items-center justify-center' : ' items-center justify-between'"
                                    x-data="{ tooltip: false }"
                                    @mouseenter="tooltip = true"
                                    @mouseleave="tooltip = false"
                                    @click="open = !open"
                                >
                                    <div class="flex items-center gap-3">
                                        <div class="">
                                            <svg
                                                class="size-6 text-gray-500"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                />
                                            </svg>

                                        </div>
                                        <span
                                            class="text-base font-medium"
                                            x-show="sidebarOpen"
                                        >وبلاگ</span>
                                    </div>
                                    <svg
                                        class="h-4 w-4 transition-transform"
                                        x-show="sidebarOpen"
                                        :class="{ 'rotate-180': open }"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                    >
                                        <path
                                            fill-rule="evenodd"
                                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                            clip-rule="evenodd"
                                        ></path>
                                    </svg>
                                    <div
                                        class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                        x-show="tooltip && !sidebarOpen"
                                        x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 translate-x-2"
                                        x-transition:enter-end="opacity-100 translate-x-0"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 translate-x-0"
                                        x-transition:leave-end="opacity-0 translate-x-2"
                                    >
                                        وبلاگ
                                    </div>
                                </button>
                                <div
                                    class="space-y-1 bg-gray-50 p-3"
                                    style="display: none;"
                                    x-show="open"
                                    x-transition
                                >
                                    <a
                                        class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                        href="/dashboard/articles"
                                        x-data="{ tooltip: false }"
                                        @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false"
                                    >
                                        <span
                                            class="text-gray-300"
                                            x-show="sidebarOpen"
                                        >-</span>
                                        <span x-show="sidebarOpen">مدیریت وبلاگ</span>
                                        <svg
                                            class="size-6 shrink-0 text-gray-500"
                                            x-show="!sidebarOpen"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                            />
                                        </svg>
                                        <div
                                            class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                            x-show="tooltip && !sidebarOpen"
                                            x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0 translate-x-2"
                                            x-transition:enter-end="opacity-100 translate-x-0"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100 translate-x-0"
                                            x-transition:leave-end="opacity-0 translate-x-2"
                                        >
                                            مدیریت وبلاگ
                                        </div>
                                    </a>
                                    <a
                                        class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                        href="/dashboard/articles/comments"
                                        x-data="{ tooltip: false }"
                                        @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false"
                                    >
                                        <span
                                            class="text-gray-300"
                                            x-show="sidebarOpen"
                                        >-</span>
                                        <span x-show="sidebarOpen">مرور و مدیریت نظرات مقالات</span>
                                        <svg
                                            class="size-6 shrink-0 text-gray-500"
                                            x-show="!sidebarOpen"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
                                            />
                                        </svg>

                                        <div
                                            class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                            x-show="tooltip && !sidebarOpen"
                                            x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0 translate-x-2"
                                            x-transition:enter-end="opacity-100 translate-x-0"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100 translate-x-0"
                                            x-transition:leave-end="opacity-0 translate-x-2"
                                        >
                                            مرور و مدیریت نظرات مقالات
                                        </div>
                                    </a>
                                    <a
                                        class="relative flex items-center gap-3 rounded-lg px-4 py-2 text-sm text-gray-700 transition-all hover:bg-gray-100"
                                        href="/dashboard/categories"
                                        x-data="{ tooltip: false }"
                                        @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false"
                                    >
                                        <span
                                            class="text-gray-300"
                                            x-show="sidebarOpen"
                                        >-</span>
                                        <span x-show="sidebarOpen">دسته بندی ها</span>
                                        <svg
                                            class="size-6 shrink-0 text-gray-500"
                                            x-show="!sidebarOpen"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"
                                            />
                                        </svg>
                                        <div
                                            class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                            x-show="tooltip && !sidebarOpen"
                                            x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0 translate-x-2"
                                            x-transition:enter-end="opacity-100 translate-x-0"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100 translate-x-0"
                                            x-transition:leave-end="opacity-0 translate-x-2"
                                        >
                                            دسته بندی ها
                                        </div>
                                    </a>
                                </div>
                            </li>
                        @endcanany

                        <!-- Notifications -->
                        @can('show-settings')
                            <li class="border-b border-gray-200">
                                <a
                                    class="group relative flex items-center gap-3 rounded-lg px-4 py-3 text-gray-700 transition-all hover:bg-gray-100"
                                    href="/dashboard/notification-manager"
                                    :class="!sidebarOpen ? ' items-center justify-center' : ''"
                                    x-data="{ tooltip: false }"
                                    @mouseenter="tooltip = true"
                                    @mouseleave="tooltip = false"
                                >
                                    <div class="">
                                        <svg
                                            class="size-6 text-gray-500"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"
                                            />
                                        </svg>

                                    </div>
                                    <span
                                        class="text-base font-medium"
                                        x-show="sidebarOpen"
                                    >نوتیفیکشن ها</span>
                                    <div
                                        class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                        x-show="tooltip && !sidebarOpen"
                                        x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 translate-x-2"
                                        x-transition:enter-end="opacity-100 translate-x-0"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 translate-x-0"
                                        x-transition:leave-end="opacity-0 translate-x-2"
                                    >
                                        نوتیفیکشن ها
                                    </div>
                                </a>
                            </li>
                        @endcan

                        @can('show-host-check-index')
                            <li class="border-b border-gray-200">
                                <a
                                    class="group relative flex items-center gap-3 rounded-lg px-4 py-3 text-gray-700 transition-all hover:bg-gray-100"
                                    href="/dashboard/host-check"
                                    :class="!sidebarOpen ? ' items-center justify-center' : ''"
                                    x-data="{ tooltip: false }"
                                    @mouseenter="tooltip = true"
                                    @mouseleave="tooltip = false"
                                >
                                    <div class="">
                                        <svg
                                            class="size-6 text-gray-500"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"
                                            />
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"
                                            />
                                        </svg>

                                    </div>
                                    <span
                                        class="text-base font-medium"
                                        x-show="sidebarOpen"
                                    >گزارش بررسی سایت ها</span>
                                    <div
                                        class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                        x-show="tooltip && !sidebarOpen"
                                        x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 translate-x-2"
                                        x-transition:enter-end="opacity-100 translate-x-0"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 translate-x-0"
                                        x-transition:leave-end="opacity-0 translate-x-2"
                                    >
                                        گزارش بررسی سایت ها
                                    </div>
                                </a>
                            </li>
                        @endcan
                        @can('show-utm-report')
                            <li class="border-b border-gray-200">
                                <a
                                    class="group relative flex items-center gap-3 rounded-lg px-4 py-3 text-gray-700 transition-all hover:bg-gray-100"
                                    href="/dashboard/utm-reports"
                                    :class="!sidebarOpen ? ' items-center justify-center' : ''"
                                    x-data="{ tooltip: false }"
                                    @mouseenter="tooltip = true"
                                    @mouseleave="tooltip = false"
                                >
                                    <div class="">
                                        <svg
                                            class="size-6 text-gray-500"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5M9 11.25v1.5M12 9v3.75m3-6v6"
                                            />
                                        </svg>

                                    </div>
                                    <span
                                        class="text-base font-medium"
                                        x-show="sidebarOpen"
                                    >گزارش UTM</span>
                                    <div
                                        class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                        x-show="tooltip && !sidebarOpen"
                                        x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 translate-x-2"
                                        x-transition:enter-end="opacity-100 translate-x-0"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 translate-x-0"
                                        x-transition:leave-end="opacity-0 translate-x-2"
                                    >
                                        گزارش UTM
                                    </div>
                                </a>
                            </li>
                        @endcan
                        <li>
                            <a
                                class="group relative flex items-center gap-3 rounded-lg px-4 py-3 text-gray-700 transition-all hover:bg-gray-100"
                                href="/dashboard/profile"
                                :class="!sidebarOpen ? ' items-center justify-center' : ''"
                                x-data="{ tooltip: false }"
                                @mouseenter="tooltip = true"
                                @mouseleave="tooltip = false"
                            >
                                <div class="">

                                    <svg
                                        class="size-6 text-gray-500"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                                        />
                                    </svg>

                                </div>
                                <span
                                    class="text-base font-medium"
                                    x-show="sidebarOpen"
                                >پروفایل من</span>
                                <div
                                    class="absolute right-full top-1/2 z-[9999999] ml-2 w-max max-w-xs -translate-y-1/2 transform whitespace-nowrap rounded-md bg-gray-800 px-3 py-2 text-xs text-white shadow-lg"
                                    x-show="tooltip && !sidebarOpen"
                                    x-transition:enter="transition ease-out duration-200"
                                    x-transition:enter-start="opacity-0 translate-x-2"
                                    x-transition:enter-end="opacity-100 translate-x-0"
                                    x-transition:leave="transition ease-in duration-150"
                                    x-transition:leave-start="opacity-100 translate-x-0"
                                    x-transition:leave-end="opacity-0 translate-x-2"
                                >
                                    پروفایل من
                                </div>
                            </a>
                        </li>
                    </ul>

                </nav>

            </div>
            <div
                class="min-h-screen w-full"
                :class="sidebarOpen ? 'md:pr-[20rem]' : 'md:pr-20'"
            >
                <div
                    class="flex w-full items-center justify-between border-b border-gray-200 bg-white p-3 px-5 text-gray-500">
                    <div>
                        <button
                            class="md:hidden"
                            type="button"
                            @click="$store.toggleDrawerRight.on = true;$store.lock.toggle()"
                        >
                            <svg
                                class="size-7"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M3.75 6.75h16.5M3.75 12h16.5M12 17.25h8.25"
                                ></path>
                            </svg>

                        </button>
                        <span class="text-base font-bold max-md:hidden">شرکت ایرانی سافت</span>
                    </div>

                    <div class="flex items-center gap-1">
                        <nav
                            class="relative flex flex-row-reverse items-center gap-4"
                            x-data="{ open: false }"
                        >
                            @can('show-settings')
                                <button
                                    type="button"
                                    @click="$store.toggleSettingDrawerLeft.on = true;$store.lock.on = true"
                                >

                                    <svg
                                        class="size-6"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"
                                        />
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                                        />
                                    </svg>
                                </button>
                            @else
                                <button
                                    class="cursor-not-allowed"
                                    type="button"
                                >

                                    <svg
                                        class="size-6"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"
                                        />
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                                        />
                                    </svg>
                                </button>
                            @endcan
                            <button type="button">
                                <svg
                                    class="size-6"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"
                                    />
                                </svg>
                            </button>

                            @can('show-host-check-index')
                                <a
                                    class="text-gray-500"
                                    href="{{ route('host-check-index') }}"
                                >

                                    <svg
                                        class="size-6"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"
                                        />
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"
                                        />
                                    </svg>

                                </a>
                            @endcan

                            @can('show-utm-report')
                                <a
                                    class="text-gray-500"
                                    href="{{ route('utm-report') }}"
                                >

                                    <svg
                                        class="size-6"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18"
                                        />
                                    </svg>

                                </a>
                            @endcan

                            <button
                                class="relative rounded-3xl bg-gray-100 px-2 py-1.5 pr-4 text-left text-base max-md:hidden"
                                type="button"
                                @click="open = true"
                            >
                                <span class="flex items-center justify-end gap-2">
                                    <svg
                                        class="size-4"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="m19.5 8.25-7.5 7.5-7.5-7.5"
                                        />
                                    </svg>
                                    <span class="text-sm">{{ auth()->user()->phone }}</span>
                                    <svg
                                        class="size-6"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                                        />
                                    </svg>

                                    {{-- <span class="text-sm">{{ auth()->user()->phone }}</span> --}}
                                    {{-- <svg
                                class="size-7"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M21 12a2.25 2.25 0 0 0-2.25-2.25H15a3 3 0 1 1-6 0H5.25A2.25 2.25 0 0 0 3 12m18 0v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6m18 0V9M3 12V9m18 0a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 9m18 0V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v3"
                                />
                            </svg> --}}
                                </span>

                            </button>
                            <div
                                class="absolute left-0 top-10 z-[999] w-72 max-w-72 rounded-xl bg-white p-5 text-gray-700 shadow-lg"
                                x-cloak
                                x-show="open"
                                x-transition:enter="transition ease-out duration-300"
                                x-transition:enter-start="opacity-0 scale-90"
                                x-transition:enter-end="opacity-100 scale-100"
                                x-transition:leave="transition ease-in duration-100"
                                x-transition:leave-start="opacity-100 scale-100"
                                x-transition:leave-end="opacity-0"
                                @click.away="open = false"
                            >
                                <div class="flex items-center gap-2 border-b border-gray-200 pb-3">
                                    <img
                                        class="h-12 w-12 rounded-full border-2 border-gray-200 p-0.5"
                                        src="/assets/images/avatar.jpg"
                                        alt=""
                                    >
                                    <div class="flex flex-col gap-0.5">
                                        <span
                                            class="text-base font-bold text-gray-800">{{ auth()->user()->phone }}</span>
                                        <span
                                            class="text-sm text-gray-500">{{ auth()->user()->roles->first()->label ?? 'کاربر عادی' }}</span>
                                    </div>
                                </div>
                                <ul class="mt-4 flex flex-col gap-3">
                                    <li class="border-b border-gray-100 pb-3">
                                        <button
                                            class="flex w-full items-center gap-2 text-gray-700 transition-all hover:text-red-500"
                                            type="button"
                                            wire:click="$dispatch('openModal', { component: 'dashboard.users.change-balance-wallet.change-balance-wallet' })"
                                        >
                                            <svg
                                                class="size-6 text-gray-500"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"
                                                />
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"
                                                />
                                            </svg>

                                            <span class="text-sm">افزایش / کاهش موجودی</span>

                                        </button>
                                    </li>
                                    <li class="border-b border-gray-100 pb-3">
                                        <button
                                            class="flex w-full items-center gap-2 text-gray-700 transition-all hover:text-red-500"
                                            type="button"
                                        >
                                            <svg
                                                class="size-6 text-gray-500"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                />
                                            </svg>

                                            <span class="text-sm">تغییر مشخصات</span>

                                        </button>
                                    </li>
                                    <li class="pb-3">
                                        <a
                                            class="flex w-full items-center gap-2 text-gray-700 transition-all hover:text-red-500"
                                            type="button"
                                            href="{{ route('logout') }}"
                                        >
                                            <svg
                                                class="size-6 text-gray-500"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M8.25 9V5.25A2.25 2.25 0 0 1 10.5 3h6a2.25 2.25 0 0 1 2.25 2.25v13.5A2.25 2.25 0 0 1 16.5 21h-6a2.25 2.25 0 0 1-2.25-2.25V15m-3 0-3-3m0 0 3-3m-3 3H15"
                                                />
                                            </svg>

                                            <span class="text-sm">خروج از سامانه</span>

                                        </a>
                                    </li>
                                </ul>
                            </div>

                        </nav>

                    </div>

                </div>
                <div
                    class="mt-4 pt-0 max-md:mt-4 md:px-5"
                    :class="sidebarOpen ? '' : ''"
                >

                    @yield('content')
                </div>
            </div>
            @livewireScripts
            <script
                src="/assets/js/<EMAIL>"
                data-navigate-once
            ></script>
            <x-livewire-alert::scripts />
            @include('layouts.right-drawer')
            @include('layouts.setting-left-drawer')
            @persist('floating')
                @can('show-settings')
                    <livewire:dashboard.floating-buttons />
                @endcan
            @endpersist
            <script
                type="text/javascript"
                src="/assets/js/jalalidatepicker.min.js"
            ></script>
            <script>
                // Get DOM elements
                const menuToggle = document.getElementById('menuToggle');
                const menuItems = document.getElementById('menuItems');
                const floatingMenu = document.getElementById('floatingMenu');

                // Toggle menu function
                function toggleMenu() {
                    menuToggle.classList.toggle('active');
                    menuItems.classList.toggle('active');
                }

                // Close menu when clicking outside
                function handleClickOutside(event) {
                    if (floatingMenu.contains(event.target)) {
                        // Click was inside the menu, do nothing
                        return;
                    }
                    // Click was outside the menu, close it if open
                    if (menuItems.classList.contains('active')) {
                        menuToggle.classList.remove('active');
                        menuItems.classList.remove('active');
                    }
                }

                // Event listeners
                menuToggle.addEventListener('click', toggleMenu);
                document.addEventListener('click', handleClickOutside);

                function Comma(Num) {
                    Num = Num.toString().replace(/,/g, '');
                    if (isNaN(Num) || Num === '') {
                        return '';
                    }
                    let negative = Num[0] === '-' ? '-' : '';
                    Num = Num.replace('-', '');
                    let parts = Num.split('.');
                    let integerPart = parts[0];
                    let decimalPart = parts.length > 1 ? '.' + parts[1] : '';
                    let rgx = /(\d+)(\d{3})/;
                    while (rgx.test(integerPart)) {
                        integerPart = integerPart.replace(rgx, '$1' + ',' + '$2');
                    }
                    return negative + integerPart + decimalPart;
                }
                jalaliDatepicker.startWatch();
            </script>
            <script>
                document.addEventListener('alpine:init', () => {
                    Alpine.data('deleteModal', () => ({
                        init() {
                            // Listen for open modal events
                            this.$el.addEventListener('open-delete-modal', (e) => {
                                this.openModal(e.detail.model, e.detail.id, e.detail.title || '');
                            });
                        },
                        // ... بقیه توابع موجود در x-data
                    }));
                });
            </script>
            @stack('script')
            @stack('scripts')
            @livewire('wire-elements-modal')
    </body>

</html>
